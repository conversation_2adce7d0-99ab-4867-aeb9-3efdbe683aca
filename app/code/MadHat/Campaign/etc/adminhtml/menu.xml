<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="MadHat_Campaign::config" resource="MadHat_Campaign::madhat_campaign_campaign"
             title="Campaign Configuration" action="adminhtml/system_config/edit/section/campaign"
             module="MadHat_Campaign" parent="Magento_Backend::marketing_communications" sortOrder="250"/>
        <add id="MadHat_Campaign::madhat_campaign_campaign" title="Campaign Integration Data" translate="title"
             module="MadHat_Campaign" sortOrder="300" resource="MadHat_Campaign::madhat_campaign_campaign"
             parent="Magento_Backend::marketing_communications" action="madhat_campaign/campaign/index"/>
        <add id="MadHat_Campaign::madhat_campaign_cmsblocks" title="- Cmsblocks" module="MadHat_Campaign" sortOrder="10"
             resource="Magento_Backend::content" parent="MadHat_Campaign::madhat_campaign_campaign"
             action="madhat_campaign/cmsblocks/index" translate="true"/>
        <add id="MadHat_Campaign::madhat_campaign_catalogrule" title="- Catalogrule" module="MadHat_Campaign"
             sortOrder="20" resource="Magento_Backend::content" parent="MadHat_Campaign::madhat_campaign_campaign"
             action="madhat_campaign/catalogrule/index" translate="true"/>
        <add id="MadHat_Campaign::madhat_campaign_salesrule" title="- Salesrule" module="MadHat_Campaign" sortOrder="30"
             resource="Magento_Backend::content" parent="MadHat_Campaign::madhat_campaign_campaign"
             action="madhat_campaign/salesrule/index" translate="true"/>
    </menu>
</config>
