<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace MadHat\Campaign\Model\ResourceModel\CmsBlocks;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'entity_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \MadHat\Campaign\Model\Cmsblocks::class,
            \MadHat\Campaign\Model\ResourceModel\CmsBlocks::class
        );
    }
}

