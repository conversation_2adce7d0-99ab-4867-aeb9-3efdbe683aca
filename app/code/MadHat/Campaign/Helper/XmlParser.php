<?php

namespace MadHat\Campaign\Helper;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem\Driver\File;
use Magento\Framework\Xml\Parser;

class XmlParser
{
    protected const CAMPAIGN_XML_ARCHIVE = 'success_xml_archive';
    protected const CAMPAIGN_XML_FAIL_ARCHIVE = 'fail_xml_archive';
    protected const CAMPAIGN_XML_DIR = 'import/campaign/Inbox';
    protected $directory_list;

    protected $parser;
    protected $driverFile;

    /**
     * @param Parser $parser
     * @param DirectoryList $directory_list
     * @param File $driverFile
     * @param Config $config
     */
    public function __construct(
        Parser $parser,
        DirectoryList $directory_list,
        File $driverFile,
        Config $config
    ) {
        $this->parser = $parser;
        $this->directory_list = $directory_list;
        $this->driverFile = $driverFile;
        $this->config = $config;
    }

    public function checkCampaignXmlDirExist()
    {
        $varDir = $this->directory_list->getPath('var');
        $campaignDir = $varDir . '/' . self::CAMPAIGN_XML_DIR;
        $archiveDir = $varDir . '/' . self::CAMPAIGN_XML_DIR . '/' . self::CAMPAIGN_XML_ARCHIVE;
        $archiveFailDir = $varDir . '/' . self::CAMPAIGN_XML_DIR . '/' . self::CAMPAIGN_XML_FAIL_ARCHIVE;
        try {
            if (!$this->driverFile->isExists($campaignDir)) {
                $this->driverFile->createDirectory($varDir . '/' . 'import', $permissions = 0777);
                $this->driverFile->createDirectory($varDir . '/' . 'import' . '/' . 'campaign', $permissions = 0777);
                $this->driverFile->createDirectory($varDir . '/' . 'import' . '/' . 'campaign' . '/' . 'Inbox', $permissions = 0777);
                //$this->driverFile->createDirectory($campaignDir, $permissions = 0777);
            }
            if (!$this->driverFile->isExists($archiveDir)) {
                $this->driverFile->createDirectory($archiveDir, $permissions = 0777);
            }
            if (!$this->driverFile->isExists($archiveFailDir)) {
                $this->driverFile->createDirectory($archiveFailDir, $permissions = 0777);
            }
        } catch (\Exception $e) {
            //logger $e->getMessage()
            return false;
        }
        return true;
    }

    public function getAllCampaignXmlFiles()
    {
        $varDir = $this->directory_list->getPath('var');
        $campaignDir = $varDir . '/' . self::CAMPAIGN_XML_DIR;
        $archiveFailDir = $varDir . '/' . self::CAMPAIGN_XML_DIR . '/' . self::CAMPAIGN_XML_FAIL_ARCHIVE;
        $xmlFileArray = [];
        if ($this->driverFile->readDirectory($campaignDir)) {
            $files = $this->driverFile->readDirectory($campaignDir);
            if(is_array($files) && !empty($files)){
                foreach ($files as $file) {
                    if ($this->driverFile->isFile($file)) {
                        $ext = pathinfo($file, PATHINFO_EXTENSION);
                        if ($ext === 'xml' || $ext === 'XML') {
                            $xmlFileArray[] = $file;
                        } else {
                            //logger
                            $this->driverFile->rename($file, $archiveFailDir . '/' . basename($file));
                        }
                    }
                }
            }
        }
        return $xmlFileArray;
    }

    public function getXmlToArray($filePath): false|array|string
    {
        if ($this->driverFile->isExists($filePath) && $this->driverFile->isFile($filePath)) {
            $parsedArray = $this->parser->load($filePath)->xmlToArray();
            return $parsedArray;
        }
        return false;
    }

    public function createCampaignXmlDir($directory)
    {
        if (!$this->driverFile->isExists($directory)) {
            $this->driverFile->createDirectory($directory, $permissions = 0777);
            return true;
        }
    }

    public function moveFileToArchive($filePath, $status)
    {
        $varDir = $this->directory_list->getPath('var');
        $destination = $varDir . '/' . self::CAMPAIGN_XML_DIR . '/' . self::CAMPAIGN_XML_ARCHIVE;
        $failDestination = $varDir . '/' . self::CAMPAIGN_XML_DIR . '/' . self::CAMPAIGN_XML_FAIL_ARCHIVE;
        if ($status && $this->driverFile->isFile($filePath)) {
            //moveSucceess
            $this->driverFile->rename($filePath, $destination . '/' . basename($filePath));
            return true;
        } elseif (!$status && $this->driverFile->isFile($filePath)) {
            //moveFail
            $this->driverFile->rename($filePath, $failDestination . '/' . basename($filePath));
            return true;
        }
        return false;
    }

    public function geCampaignDirPath()
    {
        $varDir = $this->directory_list->getPath('var');
        $campaignDir = $varDir . '/' . self::CAMPAIGN_XML_DIR;
        return $campaignDir;
    }

}
